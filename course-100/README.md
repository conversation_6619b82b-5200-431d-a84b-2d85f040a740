# Zadig 新手快速入门教程 - T100

## 概述

如果您正在寻找一款好用的云原生研效管理平台，如果您已经对缝缝补补又三年的开源CI/CD工具链心灰意冷，如果您正在选型一款占尽云时代技术优势，且可私有部署&无锁定的软件工程管理平台。那么何不学习一下《Zadig 新手快速入门教程》，本课程将在 30 分钟后刷新你认知，并帮你消除各种相关的困惑。

本课程基于 Zadig 系统平台，并以一套真实的应用系统代码库为练习对象，这个应用系统具有基于 Vue 的前端服务，Golong 开发的后端服务，是典型的前后端分离的多语言微服务应用系统。为了让你快速掌握 Zadig 的基本操作技能，你将从代码库接入开始，逐步完成环境准备、工作流定义、代码构建配置、服务运行验证、Bug 修复、自动化测试管理和版本发布就绪等工作。

本教程是一组基于代码迭代和系统实操的 Workshop，目标是让你重新发现云原生开发环境的优势，同时体会在 Zadig 管理平台上各种功能角色的人员&团队是如何顺畅的协作的，并且能将学习成果立刻复用到您当前实际的工作项目中。

## 课程安排

本教程包含所有操作步骤说明，以及视频讲解。

* 环境准备 - 2 分钟
* 项目初始化 - 3 分钟
* 定义代码构建 - 5 分钟
* 首次运行服务 - 5 分钟
* 修复已知 Bug - 5 分钟
* 自动化测试 - 5 分钟
* 版本发布 - 5 分钟

## 学习支持

Zadig 公司的小伙伴们为本教程提供了学员支持微信群，后续成长高阶课程，特定应用场景研讨等等支持服务。
