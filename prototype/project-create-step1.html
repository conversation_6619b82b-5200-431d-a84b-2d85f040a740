<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>创建项目 - DevOps 平台</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f7fa;
      height: 100vh;
      overflow: hidden;
    }

    .container {
      display: flex;
      height: 100vh;
    }

    .sidebar {
      width: 280px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      display: ;
      flex-direction: column;
      box-shadow: 2px 0 10px rgba(0,0,0,0.1);
      position: relative;
      overflow: hidden;
    }

    .sidebar::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
      pointer-events: none;
    }

    .logo {
      padding: 20px;
      border-bottom: 1px solid rgba(255,255,255,0.1);
      text-align: center;
      position: relative;
      z-index: 2;
    }

    .logo h1 {
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 5px;
      text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .logo p {
      font-size: 12px;
      opacity: 0.8;
    }

    .nav-section {
      flex: 1;
      position: relative;
      z-index: 2;
    }

    .nav-title {
      padding: 20px;
      font-size: 14px;
      font-weight: 600;
      opacity: 0.9;
      border-bottom: 1px solid rgba(255,255,255,0.1);
      background: rgba(255,255,255,0.05);
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .nav-items {
      padding: 10px 0;
    }

    .nav-item {
      display: flex;
      align-items: center;
      padding: 12px 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .nav-item::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 0;
      background: rgba(255,255,255,0.15);
      transition: width 0.3s ease;
    }

    .nav-item:hover {
      background: rgba(255,255,255,0.1);
      transform: translateX(5px);
    }

    .nav-item:hover::before {
      width: 4px;
    }

    .nav-item.active {
      background: rgba(255,255,255,0.15);
      transform: translateX(5px);
    }

    .nav-item.active::before {
      width: 4px;
      background: #ffd700;
    }

    .nav-icon {
      width: 20px;
      height: 20px;
      margin-right: 12px;
      opacity: 0.9;
    }

    .nav-text {
      font-size: 14px;
      font-weight: 500;
    }

    .main-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      background: #f8fafc;
    }

    .header {
      background: white;
      padding: 0 30px;
      height: 70px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-shadow: 0 2px 10px rgba(0,0,0,0.05);
      border-bottom: 1px solid #e2e8f0;
    }

    .header-left {
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .back-btn {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 15px;
      background: #f1f5f9;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      color: #64748b;
      text-decoration: none;
      font-size: 14px;
      transition: all 0.2s ease;
    }

    .back-btn:hover {
      background: #e2e8f0;
      color: #475569;
    }

    .header-left h2 {
      color: #2d3748;
      font-size: 24px;
      font-weight: 700;
    }

    .progress-container {
      background: white;
      padding: 30px;
      border-bottom: 1px solid #e2e8f0;
    }

    .progress-steps {
      display: flex;
      align-items: center;
      justify-content: center;
      max-width: 800px;
      margin: 0 auto;
    }

    .step {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;
      position: relative;
    }

    .step:not(:last-child)::after {
      content: '';
      position: absolute;
      top: 20px;
      left: 60%;
      right: -40%;
      height: 2px;
      background: #e2e8f0;
      z-index: 1;
    }

    .step.active:not(:last-child)::after,
    .step.completed:not(:last-child)::after {
      background: linear-gradient(90deg, #667eea, #764ba2);
    }

    .step-circle {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #e2e8f0;
      color: #94a3b8;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 16px;
      position: relative;
      z-index: 2;
      transition: all 0.3s ease;
    }

    .step.active .step-circle {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .step.completed .step-circle {
      background: #10b981;
      color: white;
    }

    .step-label {
      margin-top: 12px;
      font-size: 14px;
      font-weight: 500;
      color: #64748b;
      text-align: center;
    }

    .step.active .step-label {
      color: #667eea;
      font-weight: 600;
    }

    .step.completed .step-label {
      color: #10b981;
    }

    .form-container {
      flex: 1;
      padding: 40px;
      overflow-y: auto;
    }

    .form-card {
      background: white;
      border-radius: 12px;
      padding: 40px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.08);
      max-width: 800px;
      margin: 0 auto;
    }

    .form-title {
      font-size: 28px;
      font-weight: 700;
      color: #2d3748;
      margin-bottom: 8px;
    }

    .form-subtitle {
      font-size: 16px;
      color: #64748b;
      margin-bottom: 30px;
    }

    .form-group {
      margin-bottom: 25px;
    }

    .form-label {
      display: block;
      font-size: 14px;
      font-weight: 600;
      color: #374151;
      margin-bottom: 8px;
    }

    .required {
      color: #ef4444;
    }

    .form-input {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      font-size: 14px;
      transition: all 0.3s ease;
      background: #f8fafc;
    }

    .form-input:focus {
      outline: none;
      border-color: #667eea;
      background: white;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-textarea {
      min-height: 100px;
      resize: vertical;
    }

    .radio-group {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .radio-item {
      display: flex;
      align-items: center;
      padding: 16px;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      background: #f8fafc;
    }

    .radio-item:hover {
      border-color: #cbd5e1;
      background: white;
    }

    .radio-item.selected {
      border-color: #667eea;
      background: #f0f4ff;
    }

    .radio-input {
      margin-right: 12px;
    }

    .radio-content {
      flex: 1;
    }

    .radio-title {
      font-size: 16px;
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 4px;
    }

    .radio-desc {
      font-size: 14px;
      color: #64748b;
    }

    .form-actions {
      display: flex;
      justify-content: space-between;
      padding-top: 30px;
      border-top: 1px solid #e2e8f0;
      margin-top: 30px;
    }

    .btn {
      padding: 12px 24px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      border: none;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }

    .btn-secondary {
      background: #f1f5f9;
      color: #64748b;
      border: 1px solid #e2e8f0;
    }

    .btn-secondary:hover {
      background: #e2e8f0;
      color: #475569;
    }

    .btn-primary {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);
    }

    .btn-primary:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .help-text {
      font-size: 12px;
      color: #64748b;
      margin-top: 6px;
    }

    .checkbox-group {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-top: 8px;
    }

    .checkbox-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      background: #f8fafc;
    }

    .checkbox-item:hover {
      border-color: #cbd5e1;
      background: white;
    }

    .checkbox-item.selected {
      border-color: #667eea;
      background: #f0f4ff;
    }

    .checkbox-input {
      margin-right: 12px;
      width: 16px;
      height: 16px;
      accent-color: #667eea;
    }

    .checkbox-content {
      flex: 1;
    }

    .checkbox-title {
      font-size: 14px;
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 2px;
    }

    .checkbox-desc {
      font-size: 12px;
      color: #64748b;
    }
  </style>
</head>
<body>
<div class="container">
  <div class="sidebar">
    <div class="logo">
      <h1>DevOps</h1>
      <p>Platform</p>
    </div>

    <div class="nav-section">
      <div class="nav-title">项目中心</div>
      <div class="nav-items">
        <div class="nav-item">
          <svg class="nav-icon" fill="currentColor" viewBox="0 0 24 24">
            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
          </svg>
          <span class="nav-text">项目概览</span>
        </div>
        <div class="nav-item active">
          <svg class="nav-icon" fill="currentColor" viewBox="0 0 24 24">
            <path d="M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z"/>
          </svg>
          <span class="nav-text">项目列表</span>
        </div>
      </div>
    </div>

    <div class="nav-section">
      <div class="nav-title">产物中心</div>
      <div class="nav-items">
        <div class="nav-item">
          <svg class="nav-icon" fill="currentColor" viewBox="0 0 24 24">
            <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
          </svg>
          <span class="nav-text">镜像仓库</span>
        </div>
        <div class="nav-item">
          <svg class="nav-icon" fill="currentColor" viewBox="0 0 24 24">
            <path d="M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z"/>
          </svg>
          <span class="nav-text">编译构建</span>
        </div>
      </div>
    </div>
  </div>

  <div class="main-content">
    <div class="header">
      <div class="header-left">
        <a href="#" class="back-btn">
          <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
            <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
          </svg>
        </a>
        <h2>创建项目</h2>
      </div>
    </div>

    <div class="progress-container">
      <div class="progress-steps">
        <div class="step active">
          <div class="step-circle">1</div>
          <div class="step-label">新建项目</div>
        </div>
        <div class="step">
          <div class="step-circle">2</div>
          <div class="step-label">信息确认</div>
        </div>
      </div>
    </div>

    <div class="form-container">
      <div class="form-card">
        <h3 class="form-title">基本信息</h3>
        <p class="form-subtitle">填写项目的基本信息，这些信息将用于识别和管理您的项目</p>

        <form id="projectForm">
          <div class="form-group">
            <label class="form-label">
              项目名称 <span class="required">*</span>
            </label>
            <input type="text" class="form-input" id="projectName" placeholder="请输入项目名称">
            <div class="help-text">项目名称将用于在平台中显示和识别项目</div>
          </div>

          <div class="form-group">
            <label class="form-label">
              项目标识 <span class="required">*</span>
            </label>
            <input type="text" class="form-input" id="projectId" placeholder="请输入项目标识，如：my-project">
            <div class="help-text">项目标识是该项目资源的全局唯一标识符，用于该项目下所有资源的引用与更新，默认自动生成，同时支持手动指定，创建后不可更改</div>
            <div class="help-text error-text" id="projectIdError" style="display: none; color: #ef4444;">只支持小写字母和数字，特殊字符只支持中划线，最多32位</div>
          </div>

          <div class="form-group">
            <label class="form-label">项目描述</label>
            <textarea class="form-input form-textarea" id="projectDesc" placeholder="请简要描述项目的用途和功能"></textarea>
            <div class="help-text">详细的项目描述有助于团队成员理解项目目标</div>
          </div>

          <div class="form-group">
            <label class="form-label">
              项目类型 <span class="required">*</span>
            </label>
            <div class="radio-group">
              <div class="radio-item selected" onclick="selectProjectType(this, 'k8s-yaml')">
                <input type="radio" name="projectType" value="k8s-yaml" class="radio-input" checked>
                <div class="radio-content">
                  <div class="radio-title">Kubernetes YAML 项目</div>
                  <div class="radio-desc">使用原生 Kubernetes YAML 文件进行部署配置</div>
                </div>
              </div>
              <div class="radio-item" onclick="selectProjectType(this, 'k8s-helm')">
                <input type="radio" name="projectType" value="k8s-helm" class="radio-input">
                <div class="radio-content">
                  <div class="radio-title">Kubernetes Helm Chart 项目</div>
                  <div class="radio-desc">使用 Helm Chart 模板进行 Kubernetes 应用打包和部署</div>
                </div>
              </div>
              <div class="radio-item" onclick="selectProjectType(this, 'other')">
                <input type="radio" name="projectType" value="other" class="radio-input">
                <div class="radio-content">
                  <div class="radio-title">其他项目</div>
                  <div class="radio-desc">自定义项目类型，可配置其他部署方式</div>
                </div>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">
              项目部署集群 <span class="required">*</span>
            </label>
            <div class="help-text" style="margin-bottom: 12px;">选择项目可以部署到的 Kubernetes 集群，至少选择一个集群</div>
            <div class="checkbox-group">
              <div class="checkbox-item" onclick="toggleCluster(this, 'dev-cluster-01')">
                <input type="checkbox" name="deployClusters" value="dev-cluster-01" class="checkbox-input">
                <div class="checkbox-content">
                  <div class="checkbox-title">dev-cluster-01 (华东)</div>
                  <div class="checkbox-desc">开发环境集群 - 适用于开发和测试阶段</div>
                </div>
              </div>
              <div class="checkbox-item" onclick="toggleCluster(this, 'qa-cluster-ap-south')">
                <input type="checkbox" name="deployClusters" value="qa-cluster-ap-south" class="checkbox-input">
                <div class="checkbox-content">
                  <div class="checkbox-title">qa-cluster-ap-south (亚太南)</div>
                  <div class="checkbox-desc">QA测试集群 - 用于质量保证和集成测试</div>
                </div>
              </div>
              <div class="checkbox-item" onclick="toggleCluster(this, 'prod-cluster-us-east')">
                <input type="checkbox" name="deployClusters" value="prod-cluster-us-east" class="checkbox-input">
                <div class="checkbox-content">
                  <div class="checkbox-title">prod-cluster-us-east (美东)</div>
                  <div class="checkbox-desc">生产环境集群 - 用于正式环境部署</div>
                </div>
              </div>
              <div class="checkbox-item" onclick="toggleCluster(this, 'shared-cluster-01')">
                <input type="checkbox" name="deployClusters" value="shared-cluster-01" class="checkbox-input">
                <div class="checkbox-content">
                  <div class="checkbox-title">shared-cluster-01 (共享)</div>
                  <div class="checkbox-desc">共享集群 - 多项目共享的资源集群</div>
                </div>
              </div>
            </div>
          </div>

          <div class="form-actions">
            <a href="#" class="btn btn-secondary">取消</a>
            <button type="button" class="btn btn-primary" onclick="navigateTo('project-create-step6.html')">
              下一步
              <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                <path d="M4 11h12.17l-5.59-5.59L12 4l8 8-8 8-1.41-1.41L16.17 13H4v-2z"/>
              </svg>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<script>
  function selectProjectType(element, type) {
    // Remove selected class from all radio items
    document.querySelectorAll('.radio-item').forEach(item => {
      item.classList.remove('selected');
    });

    // Add selected class to clicked item
    element.classList.add('selected');

    // Update radio input
    element.querySelector('.radio-input').checked = true;
  }

  function toggleCluster(element, clusterId) {
    const checkbox = element.querySelector('.checkbox-input');

    // Toggle checkbox state
    checkbox.checked = !checkbox.checked;

    // Update visual state
    if (checkbox.checked) {
      element.classList.add('selected');
    } else {
      element.classList.remove('selected');
    }
  }

  function validateForm() {
    const projectName = document.getElementById('projectName').value.trim();
    const projectId = document.getElementById('projectId').value.trim();

    if (!projectName) {
      alert('请输入项目名称');
      return false;
    }

    if (!projectId) {
      alert('请输入项目标识');
      return false;
    }

    // Validate project ID format
    if (!validateProjectIdFormat(projectId)) {
      alert('项目标识格式不正确，只支持小写字母和数字，特殊字符只支持中划线，最多32位');
      return false;
    }

    // Validate cluster selection
    const selectedClusters = document.querySelectorAll('input[name="deployClusters"]:checked');
    if (selectedClusters.length === 0) {
      alert('请至少选择一个部署集群');
      return false;
    }

    return true;
  }

  function nextStep() {
    if (validateForm()) {
      // Collect selected clusters
      const selectedClusters = [];
      document.querySelectorAll('input[name="deployClusters"]:checked').forEach(checkbox => {
        selectedClusters.push({
          value: checkbox.value,
          label: checkbox.parentElement.querySelector('.checkbox-title').textContent
        });
      });

      // Save form data (in real app, would send to server)
      const formData = {
        projectName: document.getElementById('projectName').value,
        projectId: document.getElementById('projectId').value,
        projectDesc: document.getElementById('projectDesc').value,
        projectType: document.querySelector('input[name="projectType"]:checked').value,
        deployClusters: selectedClusters
      };

      // Store data in localStorage for the confirmation page
      localStorage.setItem('projectFormData', JSON.stringify(formData));

      console.log('项目信息:', formData);
      alert('第一步完成！在实际应用中，这里会进入下一步配置。');
    }
  }

  // Validate project ID in real-time
  document.getElementById('projectId').addEventListener('input', function(e) {
    const projectId = e.target.value;
    const errorElement = document.getElementById('projectIdError');
    const inputElement = e.target;

    // Check if project ID is valid
    const isValid = validateProjectIdFormat(projectId);

    if (projectId && !isValid) {
      // Show error message and add error styling
      errorElement.style.display = 'block';
      inputElement.style.borderColor = '#ef4444';
      inputElement.style.background = '#fef2f2';
    } else {
      // Hide error message and remove error styling
      errorElement.style.display = 'none';
      inputElement.style.borderColor = '#e2e8f0';
      inputElement.style.background = '#f8fafc';
    }
  });

  function validateProjectIdFormat(projectId) {
    // Check length
    if (projectId.length > 32) {
      return false;
    }

    // Check format: only lowercase letters, numbers, and hyphens
    // Must start and end with alphanumeric character
    const projectIdRegex = /^[a-z0-9][a-z0-9-]*[a-z0-9]$|^[a-z0-9]$/;
    return projectIdRegex.test(projectId);
  }
</script>

</body>
</html>
<script>
  function navigateTo(page) {
    window.location.href = page;
  }
</script>