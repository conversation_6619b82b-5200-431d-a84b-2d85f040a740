<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务发布</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f7fa;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }

        .form-container {
            background-color: #fff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
            width: 100%;
            max-width: 800px;
            overflow: hidden;
        }

        .form-header {
            background-color: #f8f8f8;
            padding: 18px 24px;
            border-bottom: 1px solid #e5e7eb;
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .form-body {
            padding: 24px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #555;
            margin-bottom: 8px;
        }

        .form-input,
        .form-textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #dcdcdc;
            border-radius: 4px;
            font-size: 14px;
            color: #333;
            box-sizing: border-box;
        }

        .form-input:focus,
        .form-textarea:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
        }

        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .select-services-wrapper {
            display: flex;
            align-items: center;
            border: 1px solid #dcdcdc;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            position: relative;
            background-color: #fff;
            min-height: 40px;
        }

        .select-services-wrapper.focused {
            border-color: #2563eb;
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
        }

        .selected-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            flex-grow: 1;
            align-items: center;
        }

        .tag {
            background-color: #e6f7ff;
            color: #1890ff;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .dropdown-arrow {
            width: 16px;
            height: 16px;
            color: #888;
            margin-left: 8px;
        }

        .service-card {
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 25px;
            background-color: #fff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            position: relative;
        }

        .service-card-close {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: #f1f5f9;
            color: #64748b;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            font-size: 16px;
            line-height: 1;
        }

        .service-card-close:hover {
            background-color: #e2e8f0;
            color: #475569;
        }

        .service-card-title {
            font-size: 17px;
            font-weight: 600;
            color: #333;
            margin-bottom: 18px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e5e7eb;
        }

        .resource-detection-title {
            font-size: 14px;
            font-weight: 500;
            color: #555;
            margin-bottom: 12px;
        }

        .resource-item {
            display: flex;
            align-items: center;
            font-size: 13px;
            color: #666;
            margin-bottom: 8px;
        }

        .resource-item svg {
            width: 15px;
            height: 15px;
            margin-right: 8px;
            color: #4a5568;
        }

        .variable-config-title {
            font-size: 14px;
            font-weight: 500;
            color: #555;
            margin-top: 25px;
            margin-bottom: 12px;
        }

        .variable-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .variable-label {
            font-size: 14px;
            color: #333;
            width: 90px;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .variable-input-field {
            flex-grow: 1;
            padding: 8px 10px;
            border: 1px solid #dcdcdc;
            border-radius: 4px;
            font-size: 14px;
            color: #333;
        }

        .question-icon {
            width: 14px;
            height: 14px;
            color: #999;
            cursor: help;
        }

        .form-footer {
            padding: 16px 24px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            background-color: #f8f8f8;
        }

        .btn {
            padding: 8px 20px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .btn-cancel {
            background-color: #fff;
            color: #555;
            border-color: #dcdcdc;
        }

        .btn-cancel:hover {
            background-color: #f0f0f0;
        }

        .btn-preview {
            background-color: #e6f7ff;
            color: #1890ff;
            border-color: #91d5ff;
        }

        .btn-preview:hover {
            background-color: #bae7ff;
        }

        .btn-confirm {
            background-color: #2563eb;
            color: #fff;
        }

        .btn-confirm:hover {
            background-color: #1d4ed8;
        }

        .services-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #dcdcdc;
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .service-option {
            padding: 10px 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: background-color 0.2s ease;
        }

        .service-option:hover {
            background-color: #f5f5f5;
        }

        .service-option input[type="checkbox"] {
            margin: 0;
        }

        .image-selection-title {
            font-size: 14px;
            font-weight: 500;
            color: #555;
            margin-top: 25px;
            margin-bottom: 12px;
        }

        .image-select {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #dcdcdc;
            border-radius: 4px;
            font-size: 14px;
            color: #333;
            background-color: white;
        }

        .image-select:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
        }

        .service-card.hidden {
            display: none;
        }

    </style>
</head>
<body>
    <div class="form-container">
        <!-- <div class="form-header">服务发布</div> -->
        <div class="form-body">
            <div class="form-group">
                <label class="form-label">发布环境</label>
                <input type="text" class="form-input" value="env-test" readonly>
            </div>
            <div class="form-group">
                <label class="form-label">发布描述</label>
                <textarea class="form-textarea" placeholder="输入本次发布的描述"></textarea>
            </div>
            <div class="form-group">
                <label class="form-label">选择服务 *</label>
                <div class="select-services-wrapper" onclick="toggleServicesDropdown()">
                    <div class="selected-tags" id="selectedTags">
                        <span style="color: #999; font-size: 14px;">请选择要发布的服务</span>
                    </div>
                    <svg class="dropdown-arrow" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M7 10l5 5 5-5H7z"/>
                    </svg>
                    <div class="services-dropdown" id="servicesDropdown">
                        <div class="service-option">
                            <input type="checkbox" id="service-backend" value="backend" onchange="toggleService(this)">
                            <label for="service-backend">backend</label>
                        </div>
                        <div class="service-option">
                            <input type="checkbox" id="service-frontend" value="frontend" onchange="toggleService(this)">
                            <label for="service-frontend">frontend</label>
                        </div>
                        <div class="service-option">
                            <input type="checkbox" id="service-database" value="database" onchange="toggleService(this)">
                            <label for="service-database">database</label>
                        </div>
                        <div class="service-option">
                            <input type="checkbox" id="service-cache" value="cache" onchange="toggleService(this)">
                            <label for="service-cache">cache</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Backend Service Card -->
            <div class="service-card hidden" id="backend-card" data-service="backend">
                <button class="service-card-close" onclick="removeService('backend')">×</button>
                <div class="service-card-title">backend</div>

                <div class="image-selection-title">镜像选择</div>
                <select class="image-select" id="backend-image">
                    <option value="backend:latest">backend:latest (最新版本)</option>
                    <option value="backend:v1.2.3">backend:v1.2.3 (稳定版本)</option>
                    <option value="backend:v1.2.2">backend:v1.2.2 (上一版本)</option>
                    <option value="backend:dev-20241201">backend:dev-20241201 (开发版本)</option>
                </select>

                <div class="resource-detection-title">资源探测</div>
                <div class="resource-list">
                    <div class="resource-item">
                        <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                        Deployment/backend-api
                    </div>
                    <div class="resource-item">
                        <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                        ConfigMap/backend-config
                    </div>
                    <div class="resource-item">
                        <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                        Service/backend-service
                    </div>
                </div>
                <div class="variable-config-title">变量配置</div>
                <div class="variable-list">
                    <div class="variable-item">
                        <label class="variable-label">VERSION <svg class="question-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm0-4h-2V7h2v8z"/></svg></label>
                        <input type="text" class="variable-input-field" value="v1.23">
                    </div>
                    <div class="variable-item">
                        <label class="variable-label">MYSQL <svg class="question-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm0-4h-2V7h2v8z"/></svg></label>
                        <input type="text" class="variable-input-field" value="10.218.34.12">
                    </div>
                </div>
            </div>

            <!-- Frontend Service Card -->
            <div class="service-card hidden" id="frontend-card" data-service="frontend">
                <button class="service-card-close" onclick="removeService('frontend')">×</button>
                <div class="service-card-title">frontend</div>

                <div class="image-selection-title">镜像选择</div>
                <select class="image-select" id="frontend-image">
                    <option value="frontend:latest">frontend:latest (最新版本)</option>
                    <option value="frontend:v2.1.0">frontend:v2.1.0 (稳定版本)</option>
                    <option value="frontend:v2.0.5">frontend:v2.0.5 (上一版本)</option>
                    <option value="frontend:dev-20241201">frontend:dev-20241201 (开发版本)</option>
                </select>

                <div class="resource-detection-title">资源探测</div>
                <div class="resource-list">
                    <div class="resource-item">
                        <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                        Deployment/frontend-web
                    </div>
                    <div class="resource-item">
                        <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                        ConfigMap/frontend-config
                    </div>
                    <div class="resource-item">
                        <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                        Service/frontend-service
                    </div>
                    <div class="resource-item">
                        <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                        Ingress/frontend-ingress
                    </div>
                </div>
                <div class="variable-config-title">变量配置</div>
                <div class="variable-list">
                    <div class="variable-item">
                        <label class="variable-label">VERSION <svg class="question-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm0-4h-2V7h2v8z"/></svg></label>
                        <input type="text" class="variable-input-field" value="v2.1.0">
                    </div>
                    <div class="variable-item">
                        <label class="variable-label">HOST <svg class="question-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm0-4h-2V7h2v8z"/></svg></label>
                        <input type="text" class="variable-input-field" value="op.scitix.ai">
                    </div>
                </div>
            </div>

            <!-- Database Service Card -->
            <div class="service-card hidden" id="database-card" data-service="database">
                <button class="service-card-close" onclick="removeService('database')">×</button>
                <div class="service-card-title">database</div>

                <div class="image-selection-title">镜像选择</div>
                <select class="image-select" id="database-image">
                    <option value="mysql:8.0">mysql:8.0 (推荐版本)</option>
                    <option value="mysql:5.7">mysql:5.7 (兼容版本)</option>
                    <option value="postgres:14">postgres:14 (PostgreSQL)</option>
                    <option value="redis:7.0">redis:7.0 (Redis缓存)</option>
                </select>

                <div class="resource-detection-title">资源探测</div>
                <div class="resource-list">
                    <div class="resource-item">
                        <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                        StatefulSet/database
                    </div>
                    <div class="resource-item">
                        <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                        Service/database-service
                    </div>
                    <div class="resource-item">
                        <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                        PersistentVolumeClaim/database-pvc
                    </div>
                </div>
                <div class="variable-config-title">变量配置</div>
                <div class="variable-list">
                    <div class="variable-item">
                        <label class="variable-label">DB_NAME <svg class="question-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm0-4h-2V7h2v8z"/></svg></label>
                        <input type="text" class="variable-input-field" value="myapp_db">
                    </div>
                    <div class="variable-item">
                        <label class="variable-label">DB_USER <svg class="question-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm0-4h-2V7h2v8z"/></svg></label>
                        <input type="text" class="variable-input-field" value="app_user">
                    </div>
                </div>
            </div>

            <!-- Cache Service Card -->
            <div class="service-card hidden" id="cache-card" data-service="cache">
                <button class="service-card-close" onclick="removeService('cache')">×</button>
                <div class="service-card-title">cache</div>

                <div class="image-selection-title">镜像选择</div>
                <select class="image-select" id="cache-image">
                    <option value="redis:7.0">redis:7.0 (最新稳定版)</option>
                    <option value="redis:6.2">redis:6.2 (LTS版本)</option>
                    <option value="memcached:1.6">memcached:1.6 (Memcached)</option>
                    <option value="redis:7.0-alpine">redis:7.0-alpine (轻量版)</option>
                </select>

                <div class="resource-detection-title">资源探测</div>
                <div class="resource-list">
                    <div class="resource-item">
                        <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                        Deployment/cache-redis
                    </div>
                    <div class="resource-item">
                        <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                        Service/cache-service
                    </div>
                    <div class="resource-item">
                        <svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/></svg>
                        ConfigMap/cache-config
                    </div>
                </div>
                <div class="variable-config-title">变量配置</div>
                <div class="variable-list">
                    <div class="variable-item">
                        <label class="variable-label">REDIS_PORT <svg class="question-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm0-4h-2V7h2v8z"/></svg></label>
                        <input type="text" class="variable-input-field" value="6379">
                    </div>
                    <div class="variable-item">
                        <label class="variable-label">MAX_MEMORY <svg class="question-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm0-4h-2V7h2v8z"/></svg></label>
                        <input type="text" class="variable-input-field" value="256mb">
                    </div>
                </div>
            </div>

        </div>
        <div class="form-footer">
            <button class="btn btn-cancel" onclick="cancelDeploy()">取消</button>
            <button class="btn btn-preview" onclick="previewChanges()">预览变更</button>
            <button class="btn btn-confirm" onclick="confirmDeploy()">确认发布</button>
        </div>
    </div>
    <script>
        let selectedServices = new Set();

        // Toggle services dropdown
        function toggleServicesDropdown() {
            const dropdown = document.getElementById('servicesDropdown');
            const wrapper = document.querySelector('.select-services-wrapper');

            if (dropdown.style.display === 'block') {
                dropdown.style.display = 'none';
                wrapper.classList.remove('focused');
            } else {
                dropdown.style.display = 'block';
                wrapper.classList.add('focused');
            }
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const wrapper = document.querySelector('.select-services-wrapper');
            const dropdown = document.getElementById('servicesDropdown');

            if (!wrapper.contains(event.target)) {
                dropdown.style.display = 'none';
                wrapper.classList.remove('focused');
            }
        });

        // Toggle service selection
        function toggleService(checkbox) {
            const serviceName = checkbox.value;
            const serviceCard = document.getElementById(serviceName + '-card');

            if (checkbox.checked) {
                selectedServices.add(serviceName);
                if (serviceCard) {
                    serviceCard.classList.remove('hidden');
                }
            } else {
                selectedServices.delete(serviceName);
                if (serviceCard) {
                    serviceCard.classList.add('hidden');
                }
            }

            updateSelectedTags();
        }

        // Remove service
        function removeService(serviceName) {
            const checkbox = document.getElementById('service-' + serviceName);
            const serviceCard = document.getElementById(serviceName + '-card');

            if (checkbox) {
                checkbox.checked = false;
            }
            if (serviceCard) {
                serviceCard.classList.add('hidden');
            }

            selectedServices.delete(serviceName);
            updateSelectedTags();
        }

        // Update selected tags display
        function updateSelectedTags() {
            const tagsContainer = document.getElementById('selectedTags');

            if (selectedServices.size === 0) {
                tagsContainer.innerHTML = '<span style="color: #999; font-size: 14px;">请选择要发布的服务</span>';
            } else {
                tagsContainer.innerHTML = '';
                selectedServices.forEach(service => {
                    const tag = document.createElement('span');
                    tag.className = 'tag';
                    tag.innerHTML = `${service} <span onclick="removeService('${service}')" style="cursor: pointer; margin-left: 4px;">×</span>`;
                    tagsContainer.appendChild(tag);
                });
            }
        }

        // Action handlers
        function cancelDeploy() {
            if (window.parent) {
                window.parent.postMessage({type: 'closeModal'}, '*');
            } else {
                alert('取消发布');
            }
        }

        function previewChanges() {
            if (selectedServices.size === 0) {
                alert('请先选择要发布的服务');
                return;
            }

            const deployData = collectDeployData();
            console.log('预览变更数据:', deployData);

            if (window.parent) {
                window.parent.postMessage({type: 'openPreviewChangesModal', data: deployData}, '*');
            } else {
                alert('预览变更功能 - 数据已输出到控制台');
            }
        }

        function confirmDeploy() {
            if (selectedServices.size === 0) {
                alert('请先选择要发布的服务');
                return;
            }

            const deployData = collectDeployData();
            console.log('确认发布数据:', deployData);

            if (window.parent) {
                window.parent.postMessage({type: 'confirmDeploy', data: deployData}, '*');
            } else {
                alert('发布成功！数据已输出到控制台');
            }
        }

        // Collect deployment data
        function collectDeployData() {
            const environment = document.querySelector('input[readonly]').value;
            const description = document.querySelector('textarea').value;
            const services = [];

            selectedServices.forEach(serviceName => {
                const imageSelect = document.getElementById(serviceName + '-image');
                const variables = {};

                // Collect variables for this service
                const serviceCard = document.getElementById(serviceName + '-card');
                if (serviceCard) {
                    const variableInputs = serviceCard.querySelectorAll('.variable-input-field');
                    variableInputs.forEach(input => {
                        const label = input.parentElement.querySelector('.variable-label').textContent.trim();
                        variables[label.replace(/\s.*/, '')] = input.value;
                    });
                }

                services.push({
                    name: serviceName,
                    image: imageSelect ? imageSelect.value : '',
                    variables: variables
                });
            });

            return {
                environment: environment,
                description: description,
                services: services
            };
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Set environment name from parent if available
            if (window.parent && window.parent.deployEnvName) {
                document.querySelector('input[readonly]').value = window.parent.deployEnvName;
            }
        });
    </script>
</body>
</html>