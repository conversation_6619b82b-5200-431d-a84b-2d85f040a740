<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建项目 - DevOps 平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 280px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: ;
            flex-direction: column;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            pointer-events: none;
        }

        .logo {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .logo h1 {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .logo p {
            font-size: 12px;
            opacity: 0.8;
        }

        .nav-section {
            flex: 1;
            position: relative;
            z-index: 2;
        }

        .nav-title {
            padding: 20px;
            font-size: 14px;
            font-weight: 600;
            opacity: 0.9;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: rgba(255,255,255,0.05);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .nav-items {
            padding: 10px 0;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .nav-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 0;
            background: rgba(255,255,255,0.15);
            transition: width 0.3s ease;
        }

        .nav-item:hover {
            background: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }

        .nav-item:hover::before {
            width: 4px;
        }

        .nav-item.active {
            background: rgba(255,255,255,0.15);
            transform: translateX(5px);
        }

        .nav-item.active::before {
            width: 4px;
            background: #ffd700;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            opacity: 0.9;
        }

        .nav-text {
            font-size: 14px;
            font-weight: 500;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #f8fafc;
        }

        .header {
            background: white;
            padding: 0 30px;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-bottom: 1px solid #e2e8f0;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .back-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            background: #f1f5f9;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            color: #64748b;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .back-btn:hover {
            background: #e2e8f0;
            color: #475569;
        }

        .header-left h2 {
            color: #2d3748;
            font-size: 24px;
            font-weight: 700;
        }

        .progress-container {
            background: white;
            padding: 30px;
            border-bottom: 1px solid #e2e8f0;
        }

        .progress-steps {
            display: flex;
            align-items: flex-start;
            justify-content: center;
            max-width: 800px;
            margin: 0 auto;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            position: relative;
        }

        .step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 20px;
            left: 60%;
            right: -40%;
            height: 2px;
            background: #e2e8f0;
            z-index: 1;
        }

        .step.completed:not(:last-child)::after {
            background: #10b981;
        }

        .step.active:not(:last-child)::after { /* Line for active to next non-completed step */
            background: linear-gradient(90deg, #10b981, #e2e8f0); /* From green to gray */
        }
        /* Special case for the line connecting the last completed step to the active step */
        .step.completed.active_predecessor:not(:last-child)::after {
            background: #10b981; /* Solid green if it leads to another completed or the active one */
        }
        .step.active .step-circle ~ .step-label {
            color: #667eea;
            font-weight: 600;
        }


        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e2e8f0;
            color: #94a3b8;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 16px;
            position: relative;
            z-index: 2;
            transition: all 0.3s ease;
        }

        .step.active .step-circle {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .step.completed .step-circle {
            background: #10b981;
            color: white;
        }

        .step-label {
            margin-top: 12px;
            font-size: 14px;
            font-weight: 500;
            color: #64748b;
            text-align: center;
        }

        .step.active .step-label {
            color: #667eea;
            font-weight: 600;
        }

        .step.completed .step-label {
            color: #10b981;
        }

        .form-container {
            flex: 1;
            padding: 30px 40px;
            overflow-y: auto;
            background-color: #f8fafc;
        }

        .form-section {
            background-color: #ffffff;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 25px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
        }

        .form-section-title {
            font-size: 18px;
            font-weight: 600;
            color: #334155;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e2e8f0;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #475569;
            margin-bottom: 6px;
        }

        .form-group input[type="text"],
        .form-group input[type="url"],
        .form-group input[type="email"],
        .form-group input[type="password"],
        .form-group input[type="number"],
        .form-group input[type="checkbox"], /* Added checkbox styling here */
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #cbd5e1;
            border-radius: 6px;
            font-size: 14px;
            color: #334155;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
            background-color: #fff;
        }
        .form-group input[type="checkbox"] { /* Specific for checkbox size and margin */
            width: auto; /* Override 100% width */
            margin-right: 8px;
            height: 16px;
            width: 16px;
            padding: 0; /* Remove padding for checkbox */
            vertical-align: middle;
        }


        .form-group input[type="text"]:focus,
        .form-group input[type="url"]:focus,
        .form-group input[type="email"]:focus,
        .form-group input[type="password"]:focus,
        .form-group input[type="number"]:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
            outline: none;
        }

        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }
        .form-group .help-text {
            font-size: 12px;
            color: #64748b;
            margin-top: 4px;
        }


        .btn {
            padding: 10px 18px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            opacity: 0.9;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        .btn-primary:disabled { /* Style for disabled button */
            background: #b0b8d1;
            opacity: 0.7;
            cursor: not-allowed;
            box-shadow: none;
        }


        .btn-secondary {
            background-color: #f1f5f9;
            color: #475569;
            border: 1px solid #e2e8f0;
        }
        .btn-secondary:hover {
            background-color: #e2e8f0;
        }

        .btn-add-item {
            background-color: #e0f2fe;
            color: #0ea5e9;
            border: 1px dashed #7dd3fc;
        }
        .btn-add-item:hover {
            background-color: #bae6fd;
            color: #0369a1;
        }
        .btn-small {
            padding: 6px 10px;
            font-size: 12px;
        }

        .btn-danger-outline {
            background-color: transparent;
            color: #ef4444;
            border: 1px solid #f87171;
        }
        .btn-danger-outline:hover {
            background-color: #fee2e2;
            color: #b91c1c;
            border-color: #dc2626;
        }

        /* Summary Styles for Confirmation Page */
        .summary-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e9edf2; /* Lighter border */
        }
        .summary-section:last-of-type { /* Last summary section no border */
            border-bottom: none;
            padding-bottom: 0;
            margin-bottom: 0;
        }

        .summary-section h4 {
            font-size: 16px; /* Slightly smaller than section title */
            font-weight: 600;
            color: #4a5568; /* slate-600 */
            margin-bottom: 15px; /* Increased spacing */
        }

        .summary-item {
            display: flex;
            font-size: 14px;
            margin-bottom: 10px; /* Increased spacing */
            color: #475569; /* slate-600 */
            line-height: 1.6;
        }

        .summary-label {
            font-weight: 500;
            color: #334155; /* slate-700 */
            width: 130px; /* Standardized label width */
            flex-shrink: 0;
        }

        .summary-value {
            color: #64748b; /* slate-500 */
            word-break: break-word; /* Ensure long values wrap */
        }

        .summary-list {
            list-style: none;
            padding-left: 0;
        }

        .summary-list li {
            background-color: #f8fafc; /* coolGray-50 */
            padding: 12px 15px;
            border-radius: 6px; /* Slightly more rounded */
            margin-bottom: 10px;
            border: 1px solid #e2e8f0; /* coolGray-200 */
            font-size: 14px;
            transition: box-shadow 0.2s ease;
        }
        .summary-list li:hover {
            box-shadow: 0 1px 4px rgba(0,0,0,0.07);
        }


        .summary-list-item-name {
            font-weight: 600; /* Bolder name */
            color: #334155; /* slate-700 */
            display: block;
            margin-bottom: 5px;
        }

        .summary-list-item-detail {
            font-size: 13px;
            color: #64748b; /* slate-500 */
            display: block;
            line-height: 1.5;
        }
        .summary-placeholder {
            font-size: 14px;
            color: #94a3b8; /* coolGray-400 */
            padding: 15px;
            text-align: center;
            background-color: #f8fafc;
            border-radius: 6px;
            border: 1px dashed #e2e8f0;
        }

        .confirmation-section { /* Wrapper for the final confirmation message and checkbox */
            background-color: #eff6ff; /* blue-50 */
            border-left: 4px solid #3b82f6; /* blue-500 */
            padding: 20px 25px;
            margin-top: 10px; /* Space from summary */
            border-radius: 0 0 8px 8px; /* Match bottom of form-section if it's the last one */
        }
        .confirmation-section .form-section-title { /* Using existing class but can override */
            color: #1e40af; /* blue-800 */
            margin-bottom: 10px;
        }
        .confirmation-section p {
            font-size: 15px;
            color: #1d4ed8; /* blue-700 */
            margin-bottom:15px;
        }
        .confirmation-section .form-group label {
            font-weight: 500;
            color: #2563eb; /* blue-600 */
            display: flex;
            align-items: center;
        }
        .confirmation-section input[type="checkbox"] {
            accent-color: #3b82f6; /* For modern browsers to color the checkbox */
        }


        .actions-container {
            margin-top: 25px;
            padding: 20px;
            background-color: #ffffff;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            border-radius: 0 0 8px 8px;
            position: sticky;
            bottom: 0;
            z-index: 10;
        }

        .nav-section {
            flex: 1;
            position: relative;
            z-index: 2;
        }

        .nav-title {
            padding: 20px;
            font-size: 14px;
            font-weight: 600;
            opacity: 0.9;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: rgba(255,255,255,0.05);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .nav-items {
            padding: 10px 0;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            text-decoration: none;
            color: white;
        }

        .nav-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 0;
            background: rgba(255,255,255,0.15);
            transition: width 0.3s ease;
        }

        .nav-item:hover {
            background: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }

        .nav-item:hover::before {
            width: 4px;
        }

        .nav-item.active {
            background: rgba(255,255,255,0.15);
            transform: translateX(5px);
        }

        .nav-item.active::before {
            width: 4px;
            background: #ffd700;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            opacity: 0.9;
        }

        .nav-text {
            font-size: 14px;
            font-weight: 500;
        }

        /* Enhanced Project Overview Styles */
        .enhanced-project-overview {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: none;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .overview-header {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
        }

        .overview-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .overview-title-section .form-section-title {
            margin-bottom: 8px;
            color: #1e293b;
            font-size: 24px;
        }

        .overview-subtitle {
            color: #64748b;
            font-size: 15px;
            margin: 0;
            line-height: 1.5;
        }

        .project-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .info-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            display: flex;
            align-items: flex-start;
            gap: 16px;
        }

        .info-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .info-card.full-width {
            grid-column: 1 / -1;
        }

        .info-card-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #667eea;
            flex-shrink: 0;
        }

        .info-card-content {
            flex: 1;
        }

        .info-card-content h4 {
            font-size: 16px;
            font-weight: 600;
            color: #334155;
            margin: 0 0 8px 0;
        }

        .info-card-content p {
            font-size: 15px;
            color: #64748b;
            margin: 0;
            line-height: 1.5;
            word-break: break-word;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="sidebar">
        <div class="logo">
            <h1>DevOps</h1>
            <p>Platform</p>
        </div>

        <div class="nav-section">
            <div class="nav-title">项目中心</div>
            <div class="nav-items">
                <a href="#" class="nav-item" onclick="navigateTo('index.html')">
                    <svg class="nav-icon" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                    </svg>
                    <span class="nav-text">项目概览</span>
                </a>
                <a href="#" class="nav-item active" onclick="navigateTo('project-list.html')">
                    <svg class="nav-icon" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z"/>
                    </svg>
                    <span class="nav-text">项目列表</span>
                </a>
            </div>
        </div>

        <div class="nav-section">
            <div class="nav-title">产物中心</div>
            <div class="nav-items">
                <a href="#" class="nav-item" onclick="navigateTo('image-center.html')">
                    <svg class="nav-icon" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
                    </svg>
                    <span class="nav-text">镜像仓库</span>
                </a>
                <a href="#" class="nav-item" onclick="navigateTo('build-info.html')">
                    <svg class="nav-icon" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z"/>
                    </svg>
                    <span class="nav-text">编译构建中心</span>
                </a>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="header">
            <div class="header-left">
                <a href="#" class="back-btn">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
                    </svg>

                </a>
                <h2>创建项目</h2>
            </div>
        </div>

        <div class="progress-container">
            <div class="progress-steps">
                <div class="step completed">
                    <div class="step-circle">1</div>
                    <div class="step-label">新建项目</div>
                </div>
                <div class="step active">
                    <div class="step-circle">2</div>
                    <div class="step-label">信息确认</div>
                </div>
            </div>
        </div>

        <div class="form-container">
            <div class="form-section enhanced-project-overview">
                <h3 class="form-section-title">项目信息总览</h3>
                <p style="color: #64748b; font-size: 14px; margin-bottom: 25px;">请仔细检查以下为您生成的项目配置信息。确认无误后，勾选下方确认框并点击“确认创建项目”按钮以完成创建流程。</p>

                <div class="summary-section">
                    <h4>项目基本信息</h4>
                    <div class="summary-item">
                        <span class="summary-label">项目名称：</span>
                        <span class="summary-value" id="confirmProjectName">DevOps 演示项目 Alpha</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">项目标识：</span>
                        <span class="summary-value" id="confirmProjectIdentifier">devops-demo-alpha</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">项目类型：</span>
                        <span class="summary-value" id="confirmProjectType">Kubernetes YAML 项目</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">项目描述：</span>
                        <span class="summary-value" id="confirmProjectDescription">一个用于演示完整 DevOps 流程的示例应用，包含前端和后端服务。</span>
                    </div>
                </div>

                <div class="summary-section">
                    <h4>部署集群配置</h4>
                    <div class="summary-item">
                        <span class="summary-label">可部署集群：</span>
                        <div class="summary-value">
                            <ul class="summary-list" id="confirmDeployClusters">
                                <li>
                                    <span class="summary-list-item-name">dev-cluster-01 (华东)</span>
                                    <span class="summary-list-item-detail">开发环境集群 - 适用于开发和测试阶段</span>
                                </li>
                                <li>
                                    <span class="summary-list-item-name">qa-cluster-ap-south (亚太南)</span>
                                    <span class="summary-list-item-detail">QA测试集群 - 用于质量保证和集成测试</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>



            </div>

            <div class="confirmation-section">
                <h3 class="form-section-title">创建确认</h3>
                <p>您即将根据以上配置信息创建新项目。此操作完成后，项目相关的资源将被初始化。</p>
                <div class="form-group">
                    <label for="confirmCheckbox">
                        <input type="checkbox" id="confirmCheckbox" name="confirmCheckbox">
                        我已仔细阅读并核对以上所有配置信息，确认无误。
                    </label>
                </div>
            </div>


            <div class="actions-container">
                <button type="button" class="btn btn-secondary">上一步：新建项目</button>
                <button type="button" class="btn btn-primary" id="finalCreateButton">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24" style="margin-right: 8px;">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
                    </svg>
                    确认创建项目
                </button>
            </div>
        </div>
    </div>
</div>

<script>
  // Load project data from localStorage and populate the confirmation page
  document.addEventListener('DOMContentLoaded', function() {
    const projectData = localStorage.getItem('projectFormData');

    if (projectData) {
      const data = JSON.parse(projectData);

      // Update basic project information
      document.getElementById('confirmProjectName').textContent = data.projectName || 'DevOps 演示项目 Alpha';
      document.getElementById('confirmProjectIdentifier').textContent = data.projectId || 'devops-demo-alpha';
      document.getElementById('confirmProjectDescription').textContent = data.projectDesc || '一个用于演示完整 DevOps 流程的示例应用，包含前端和后端服务。';

      // Update project type
      const projectTypeMap = {
        'k8s-yaml': 'Kubernetes YAML 项目',
        'k8s-helm': 'Kubernetes Helm Chart 项目',
        'other': '其他项目'
      };
      document.getElementById('confirmProjectType').textContent = projectTypeMap[data.projectType] || 'Kubernetes YAML 项目';

      // Update deploy clusters
      const clustersContainer = document.getElementById('confirmDeployClusters');
      if (data.deployClusters && data.deployClusters.length > 0) {
        clustersContainer.innerHTML = '';
        data.deployClusters.forEach(cluster => {
          const li = document.createElement('li');
          li.innerHTML = `
            <span class="summary-list-item-name">${cluster.label}</span>
            <span class="summary-list-item-detail">已选择用于项目部署</span>
          `;
          clustersContainer.appendChild(li);
        });
      }
    }
  });

  function navigateTo(page) {
    window.location.href = page;
  }

  // Handle final project creation
  document.getElementById('finalCreateButton').addEventListener('click', function() {
    const confirmCheckbox = document.getElementById('confirmCheckbox');

    if (!confirmCheckbox.checked) {
      alert('请先勾选确认框，确认您已仔细阅读并核对所有配置信息。');
      return;
    }

    // In a real application, this would send the data to the server
    const projectData = localStorage.getItem('projectFormData');
    if (projectData) {
      const data = JSON.parse(projectData);
      console.log('创建项目:', data);

      alert('项目创建成功！在实际应用中，这里会跳转到项目详情页面。');

      // Clear the stored data
      localStorage.removeItem('projectFormData');

      // Navigate to project list or project detail page
      // window.location.href = 'project-list.html';
    }
  });
</script>

</body>
</html>